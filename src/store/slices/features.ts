import { createSlice } from '@reduxjs/toolkit';

import type { GalleryLayout, GalleryStyle } from 'types/Gallery';
import type { Story } from 'types/Story';
import type { WidgetPosition } from 'types/businessProfiles';
import type { RegionalNewsletter } from 'types/mail';

export type Feature<T = never> =
  | {
      enabled: false;
    }
  | ([T] extends [never]
      ? { enabled: true }
      : {
          data: T;
          enabled: true;
        });

export interface AdServingFeature {
  apsPublisherId: string;
  autoRefreshInterval: number;
  bottomAnchorAdPosition: number;
  doubleClickCat: string;
  doubleClickRegion: string;
  doubleClickSite: string;
  doubleClickState: string;
  doubleClickZone: string;
  enableLazyLoadAbTest: boolean;
  fetchMarginPercent: number;
  fuseLibraryVersion: string;
  mobileScaling: number;
  renderMarginPercent: number;
  useBottomAnchorAd: boolean;
  useMantis: boolean;
  usePublift: boolean;
}

export interface BusinessProfilesFeature {
  location: string;
  radius: number;
  widgetPosition: WidgetPosition;
}

export enum ClassifiedsLayout {
  Legacy,
  Masonry,
  Cards,
}

export interface ClassifiedsFeature {
  layout: ClassifiedsLayout;
  showSimilarAds: boolean;
}

export const TRIBUTES_FUNERALS_SLUG = 'tributes-funerals';

export interface FacebookNewsFeature {
  appId: string;
  automatic: boolean;
  scope: string;
}

export interface OwnLocalFeature {
  hasDirectory: boolean;
  partnerId: string;
}

export interface ClusterAdditionalSiteAccess {
  logoMastheadOnly: string;
  name: string;
  url: string;
}

export interface EnterpriseSubscription {
  iconUrl: string;
  myAccountLogoPath?: string;
  name: string;
  pianoConnectionName: string;
}

export enum CompleteProfileEnrichment {
  NONE = 'NONE',
  AGS = 'AGS',
  REGIONALS = 'REGIONALS',
}

export const enum PaywallCampaignTermDisplay {
  ALL = 'all',
  ANNUAL = 'annual',
  MONTHLY = 'monthly',
}

export const enum PaywallCampaignTermFocus {
  MONTHLY = 'monthly',
  ANNUAL = 'annual',
}

export const enum PaywallCampaignPillPosition {
  MONTHLY = 'monthly',
  ANNUAL = 'annual',
  TOP = 'top',
}

interface PaywallCampaign {
  leadText: string;
  name: string;
  pillColour: string;
  pillPosition: PaywallCampaignPillPosition;
  pillText: string;
  pillTextColour: string;
  termDisplay: PaywallCampaignTermDisplay;
  termFocus: PaywallCampaignTermFocus;
}

export interface PianoFeature {
  aid: string;
  articlePaywallHeadingText: string;
  betaResourceId: string;
  clusterAdditionalSiteAccess?: ClusterAdditionalSiteAccess;
  completeProfileEnrichments: CompleteProfileEnrichment;
  ctaVariant: string;
  enterpriseSubscriptions: EnterpriseSubscription[];
  hasSocialScreen: boolean;
  header: string;
  hideArticleAnnualSavingsPill: boolean;
  hideSubscriberSignposts: boolean;
  isAbTesting: boolean;
  isFullwidthRecommendationContentEnabled: boolean;
  isPremiumRequest: boolean;
  isRecommendationContentEnabled: boolean;
  paywallCampaign?: PaywallCampaign;
  registrationOnly: boolean;
  siteId: string;
  subColour: string;
  subHeader: string;
  supportAuthServerPaywall: boolean;
  supportLoginApple: boolean;
  supportLoginFacebook: boolean;
  supportLoginGoogle: boolean;
  supportMonthlyAnnualPaywall: boolean;
  supportPremiumExtended: boolean;
  supportPremiumSubscription: boolean;
  supportPrintBundle: boolean;
}

export enum MailProvider {
  MAILCHIMP,
  MARKETING_CLOUD,
  MAILCHIMP_AGS,
}

export type MailFeature = {
  listId: string;
  mailchimpAccountId: string;
  mailchimpListManageUrl: string;
  marketingCloudUrl: string;
  provider: MailProvider;
} & (
  | {
      supportNewslettersLandingPage: false;
    }
  | {
      articleWidgetHeading: string;
      articleWidgetUrl: string;
      newsletters: {
        local: Record<string, RegionalNewsletter>;
        national: Record<string, RegionalNewsletter>;
      };
      supportNewslettersLandingPage: true;
      supportNewslettersSubscribeFlow: boolean;
    }
);

export interface DpeFeature {
  dpeHowtoVideoId: string;
  dpeId: string;
  dpeIndexPageUrl: string;
  dpeLatestDate?: string;
  host: string;
  version: string;
}

export interface GoogleExtendedAccessFeature {
  clientId: string;
}

export interface GoogleOptimizeFeature {
  containerId: string;
  experience: string;
  gaId: string;
}

export interface GoogleTagManagerFeature {
  containerId: string;
  envVars: string;
  measurementId: string;
}

export interface MicrosoftClarityFeature {
  clarityId: string;
}

export interface HotJarFeature {
  id: string;
}

export interface IpsosIrisAnalyticsFeature {
  classifiedsSectionId: string;
  commentSectionId: string;
  defaultSectionId: string;
  homepageSectionId: string;
  jobsSectionId: string;
  newsSectionId: string;
  sportSectionId: string;
  whatsonSectionId: string;
}

export interface MobileAppFeature {
  appAndroidPriceInfo: string;
  appIosPriceInfo: string;
  appStoreId: string;
  googlePlayId: string;
  smartBanner: boolean;
  smartBannerAppHeader: string;
}

export interface NorkonLiveblogFeature {
  assetVersion: string;
  baseUrl: string;
  scriptUrl: string;
  tenantKey: string;
  websocketUrl: string;
}

export interface PuzzleFeature {
  codeCracker: string;
  crossword: string;
  crypticCrossword: string;
  logo: string;
  subscribersOnly: boolean;
  sudoku: string;
  ultimateTrivia: string;
  wheelWords: string;
  wordSearch: string;
}

export interface PushNotificationsFeature {
  appKey: string;
  appToken: string;
  askAgain: number;
  displayThreshold: number;
  notificationIcon: string;
  vapidPublicKey: string;
  websitePushId: string;
}

export interface RoyMorganAnalyticsFeature {
  clientId: string;
  clientId2: string;
  clientId3: string;
  publisherId: string;
  publisherId2: string;
  publisherId3: string;
  websiteId: string;
  websiteId2: string;
  websiteId3: string;
}

export interface SportsHubSponsorData {
  backgroundColor?: string;
  bannerDesktopUrl?: string;
  bannerMobileUrl?: string;
  logo: string;
  name: string;
  text?: string;
  textColor?: string;
  url: string;
}

export interface SportsHubSponsorFeature {
  featuredSportSponsor: string;
  sponsorData: Record<string, SportsHubSponsorData>;
}

export enum ViafouraCommentIconTest {
  ICON,
  TEXT,
  NO_ICON,
}

export interface ViafouraFeature {
  commentIconTest: ViafouraCommentIconTest;
  enableConversationStarter: boolean;
  enableSyndication: boolean;
  sectionUuid: string;
}

export interface PhotoGalleryFeature {
  adFrequency: number;
  ctaPersistentButtonText: string;
  ctaPersistentDescription: string;
  ctaSlideButtonText: string;
  ctaSlideDescription: string;
  ctaSlideTitle: string;
  ctaUrl: string;
  layout: GalleryLayout;
  showCta: boolean;
  showInBetweenSlideAds: boolean;
  style: GalleryStyle;
}

export interface ReCaptchaV3Feature {
  publicApiKey: string;
}

export interface SkimlinksFeatureData {
  isStoryBlacklisted: boolean;
  key: string | null;
}

export interface RealEstateFeature {
  exclusiveHomesApiUrl: string;
  exclusiveWidgetEnabled: boolean;
  queryData: {
    locations?: [
      {
        postCode?: number;
        state?: string;
        suburb?: string;
      },
    ];
    sort?: {
      sortDirection?: string;
      sortingField?: string;
    };
    typeOfListing?: string;
  };
  utmSource: string;
}

export interface TaboolaFeature {
  publisherId: string;
}

export interface AdFixusFeature {
  licenseKey?: string;
  version?: string;
  versionUrl?: string;
}

export interface UgcFeature {
  ctaShareStory?: string;
  showShareStory: boolean;
  useRegion: boolean;
}

export interface ExploreTravelRecirculationFeatureData {
  latestStoryWidget: {
    limit: number;
    stories: Story[];
    storyListId: number | null;
  };
  mostPopularStoryWidget: {
    limit: number;
    stories: Story[];
    storyListId: number | null;
  };
}

export interface FeaturesState {
  adFixus: Feature<AdFixusFeature>;
  adLazyLoadGpt: Feature;
  adServing: Feature<AdServingFeature>;
  bfEsov: Feature;
  businessProfiles: Feature<BusinessProfilesFeature>;
  classifieds: Feature<ClassifiedsFeature>;
  communityRecirculation: Feature;
  communityShareContent: Feature;
  customerDataPlatform: Feature;
  dailymotionAutoPause: Feature;
  dpe: Feature<DpeFeature>;
  emags: Feature;
  exploreTravelRecirculation: Feature<ExploreTravelRecirculationFeatureData>;
  facebookNews: Feature<FacebookNewsFeature>;
  googleExtendedAccess: Feature<GoogleExtendedAccessFeature>;
  googleOptimize: Feature<GoogleOptimizeFeature>;
  googleTagManager: Feature<GoogleTagManagerFeature>;
  headlineTesting: Feature;
  hindSight: Feature;
  hotjar: Feature<HotJarFeature>;
  ipsosIrisAnalytics: Feature<IpsosIrisAnalyticsFeature>;
  liveramp: Feature;
  mail: Feature<MailFeature>;
  microsoftClarity: Feature<MicrosoftClarityFeature>;
  mobileApp: Feature<MobileAppFeature>;
  norkonLiveblog: Feature<NorkonLiveblogFeature>;
  oovvuu: Feature;
  ownLocal: Feature<OwnLocalFeature>;
  photoGallery: Feature<PhotoGalleryFeature>;
  piano: Feature<PianoFeature>;
  polar: Feature;
  pushNotifications: Feature<PushNotificationsFeature>;
  puzzle: Feature<PuzzleFeature>;
  realEstate: Feature<RealEstateFeature>;
  recaptchaV3: Feature<ReCaptchaV3Feature>;
  retently: Feature;
  royMorganAnalytics: Feature<RoyMorganAnalyticsFeature>;
  skimlinks: Feature<SkimlinksFeatureData>;
  sportsHubSponsor: Feature<SportsHubSponsorFeature>;
  taboola: Feature<TaboolaFeature>;
  teads: Feature;
  tooltips: Feature;
  ugc: Feature<UgcFeature>;
  userBookmarks: Feature;
  viafoura: Feature<ViafouraFeature>;
  weather: Feature;
}

const initFeature: Feature = {
  enabled: false,
};

const initialState: FeaturesState = {
  adFixus: initFeature,
  adLazyLoadGpt: initFeature,
  adServing: initFeature,
  bfEsov: initFeature,
  businessProfiles: initFeature,
  classifieds: initFeature,
  communityRecirculation: initFeature,
  communityShareContent: initFeature,
  customerDataPlatform: initFeature,
  dailymotionAutoPause: initFeature,
  dpe: initFeature,
  emags: initFeature,
  exploreTravelRecirculation: initFeature,
  facebookNews: initFeature,
  googleExtendedAccess: initFeature,
  googleOptimize: initFeature,
  googleTagManager: initFeature,
  headlineTesting: initFeature,
  hindSight: initFeature,
  hotjar: initFeature,
  ipsosIrisAnalytics: initFeature,
  liveramp: initFeature,
  mail: initFeature,
  microsoftClarity: initFeature,
  mobileApp: initFeature,
  norkonLiveblog: initFeature,
  oovvuu: initFeature,
  ownLocal: initFeature,
  photoGallery: initFeature,
  piano: initFeature,
  polar: initFeature,
  pushNotifications: initFeature,
  puzzle: initFeature,
  realEstate: initFeature,
  recaptchaV3: initFeature,
  retently: initFeature,
  royMorganAnalytics: initFeature,
  skimlinks: initFeature,
  sportsHubSponsor: initFeature,
  taboola: initFeature,
  teads: initFeature,
  tooltips: initFeature,
  ugc: initFeature,
  userBookmarks: initFeature,
  viafoura: initFeature,
  weather: initFeature,
};

const featuresSlice = createSlice({
  initialState,
  name: 'features',
  reducers: {},
});

export default featuresSlice;
