'use client';

import { debugGaaLog as debugLog } from 'components/GoogleExtendAccess/utils';

import { onPianoReady, pushPianoAction } from './ready';

import type { MouseEvent } from 'react';

export function pianoLogin(arg: MouseEvent): void;
export function pianoLogin(arg: boolean): void;
export function pianoLogin(): void;

export function pianoLogin(arg?: boolean | MouseEvent): void {
  let disableSignUp = false;
  if (typeof arg === 'boolean') {
    disableSignUp = arg;
  }
  onPianoReady((tp) => {
    tp.pianoId.show({
      disableSignUp,
      screen: 'login',
    });
  });
}

export function authServerLogin() {
  window.location.href = `/login?return=${encodeURIComponent(
    window.location.href,
  )}`;
}

export function pianoLogout(): void {
  onPianoReady((tp) => {
    tp.pianoId.logout().catch(console.error);
  });
  window.location.reload();
}

export function pianoRegister(): void {
  onPianoReady((tp) => {
    tp.pianoId.show({
      screen: 'register',
    });
  });
}

export function authServerRegister(url?: string) {
  window.location.href = `/register?return=${encodeURIComponent(
    url ?? window.location.href,
  )}`;
}

export function pianoLoginByToken(token: string): void {
  onPianoReady((tp) => {
    debugLog(`pianoLoginByToken ${token}`);
    pushPianoAction(['setCustomVariable', 'loginByToken', true]);
    tp.pianoId.loginByToken(token).catch(console.error);
  });
}
