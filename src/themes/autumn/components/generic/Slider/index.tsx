'use client';

import {
  faChevronLeft,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import React, { useRef, useState } from 'react';
import Slider from 'react-slick';

import { useAppSelector } from 'store/hooks';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import styles from './slider.module.css';

interface NavButtonsProps {
  showNextArrow: boolean;
  showPrevArrow: boolean;
  sliderRef: React.RefObject<Slider | null>;
}

function NavButtons({
  showNextArrow,
  showPrevArrow,
  sliderRef,
}: NavButtonsProps) {
  const handlePrevClick = () => {
    if (sliderRef.current) {
      sliderRef.current.slickPrev();
    }
  };

  const handleNextClick = () => {
    if (sliderRef.current) {
      sliderRef.current.slickNext();
    }
  };

  return (
    <>
      <button
        aria-label="Previous"
        disabled={!showPrevArrow}
        onClick={handlePrevClick}
        type="button"
      >
        <div
          className={clsx(
            'absolute inset-y-[35%] left-0 z-10 -ml-4 size-10 rounded-full bg-white p-2.5 text-center text-sm font-medium text-white ring-1 ring-gray-300 drop-shadow-md hover:opacity-90',
            { hidden: !showPrevArrow },
          )}
        >
          <FontAwesomeIcon
            className="place-self-center text-lg text-gray-700"
            icon={faChevronLeft}
          />
        </div>
      </button>
      <button
        aria-label="Next"
        disabled={!showNextArrow}
        onClick={handleNextClick}
        type="button"
      >
        <div
          className={clsx(
            'absolute inset-y-[35%] right-0 z-10 -mr-4 size-10 rounded-full bg-white p-2.5 text-center text-sm font-medium text-white ring-1 ring-gray-300 drop-shadow-md hover:opacity-90',
            { hidden: !showNextArrow },
          )}
        >
          <FontAwesomeIcon
            className="place-self-center text-lg text-gray-700"
            icon={faChevronRight}
          />
        </div>
      </button>
    </>
  );
}

interface CardSliderProps {
  childWidth?: number;
  children: React.ReactNode;
}

function CardSlider({
  childWidth,
  children,
}: CardSliderProps): React.ReactElement | null {
  const [currentSlide, setCurrentSlide] = useState(0);
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const sliderRef = useRef<Slider>(null);

  const ref = useRef<HTMLDivElement>(null);

  const slidesToShow =
    ref?.current?.offsetWidth && childWidth
      ? Math.floor(ref.current.offsetWidth / childWidth)
      : 1;
  const childrenCount = React.Children.count(children);

  const settings = {
    arrows: false,
    beforeChange: (_: number, next: number) => setCurrentSlide(next),
    dots: false,
    easing: 'easeInOut',
    infinite: false,
    slidesToScroll: slidesToShow,
    slidesToShow,
    speed: 300,
    variableWidth: true,
  };

  const showPrevArrow = currentSlide > 0;
  const showNextArrow = currentSlide + settings.slidesToShow < childrenCount;

  if (!childrenCount) {
    return null;
  }

  return (
    <div className="relative flex flex-col" ref={ref}>
      <div className="flex">
        {sliderRef && (
          <NavButtons
            showNextArrow={showNextArrow}
            showPrevArrow={showPrevArrow}
            sliderRef={sliderRef}
          />
        )}
        <div className={clsx('mb-4 w-full', styles.slickWrapper)}>
          <Slider
            className={clsx('flex flex-row', viewType, { hasTakeoverAd })}
            ref={sliderRef}
            /* eslint-disable-next-line react/jsx-props-no-spreading */
            {...settings}
          >
            {children}
          </Slider>
        </div>
      </div>
    </div>
  );
}
export default CardSlider;
