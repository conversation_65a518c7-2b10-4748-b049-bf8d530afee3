'use client';

import {
  faChevronLeft,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import React, { useEffect, useRef, useState } from 'react';
import Slider from 'react-slick';
import slugify from 'slugify';

import { useAppSelector } from 'store/hooks';
import { GameStatus, StatTypes } from 'types/playhq';
import { ResponsiveType } from 'util/device';
import { sendToGtm, setGtmDataLayer } from 'util/gtm';
import { useOnce, useResponsiveTypeFromWidth } from 'util/hooks';
import {
  fetchPlayHqGradeFixture,
  fetchPlayHqSeasonTeamsAll,
  filterTeamsByGrade,
  getCurrentRoundIndex,
  mapLogosByTeamId,
  mapPlayingSurfacesById,
  mapTeamsById,
  sortRoundGamesBySchedule,
} from 'util/playhq';

import MatchWidget from '../Match/widget';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import styles from './localmatches.module.css';

import type {
  GradeFixtureResponse,
  Ladder,
  LogoSizeMap,
  PlayingSurface,
  RoundGame,
  SeasonTeam,
} from 'types/playhq';

interface LocalMatchesProps {
  grade1Id?: string;
  grade2Id?: string;
  leagueImageId: string;
  leagueName: string;
  organisationId?: string;
  seasonId?: string;
  urlBase: string;
}

interface NavButtonsProps {
  currentSlide: number;
  hasTakeoverAd: boolean;
  initialized: boolean;
  showNextArrow: boolean;
  showPrevArrow: boolean;
  sliderRef: React.RefObject<Slider | null>;
}

export interface GameWidgetProps {
  game: RoundGame;
  ladders?: Ladder[] | null;
  leagueImageUrl?: string;
  leagueName?: string;
  playingSurfaces: Record<string, PlayingSurface>;
  // showDisplayBoard?: boolean;
  // showVenue?: boolean;
  seasonId?: string;
  seasonTeams: Record<string, SeasonTeam>;
  teamLogos: Record<string, LogoSizeMap>;
  urlBase: string;
}

function GameWidget({
  game,
  ladders,
  leagueImageUrl,
  leagueName,
  playingSurfaces,
  seasonId,
  seasonTeams,
  teamLogos,
  urlBase,
}: GameWidgetProps): React.ReactElement | null {
  const homeTeam = game.teams?.find((team) => team.isHomeTeam);
  const awayTeam = game.teams?.find((team) => !team.isHomeTeam);
  const utcStartTime = game.schedule?.[0]?.dateTime ?? '';
  const matchStatus = game.status;
  const venueName =
    playingSurfaces[game.schedule?.[0]?.playingSurfaceId ?? '']?.venue.name;
  const homeSquadName = homeTeam ? (seasonTeams[homeTeam.id]?.name ?? '') : '';
  const awaySquadName = awayTeam ? (seasonTeams[awayTeam.id]?.name ?? '') : '';
  const awaySquadPosition = ladders?.[0]?.standings?.findIndex(
    (standing) => standing.team?.id === awayTeam?.id,
  );
  const homeSquadPosition = ladders?.[0]?.standings?.findIndex(
    (standing) => standing.team?.id === homeTeam?.id,
  );
  const homeLogo =
    (homeTeam?.id && teamLogos[homeTeam.id]?.[64]?.url) ?? undefined;
  const awayLogo =
    (awayTeam?.id && teamLogos[awayTeam.id]?.[64]?.url) ?? undefined;

  const homeMatchTeam = game.match?.teams?.find(
    (team) => team.id === homeTeam?.id,
  );

  const awayMatchTeam = game.match?.teams?.find(
    (team) => team.id === awayTeam?.id,
  );

  const homeScore = homeMatchTeam?.outcome?.statistics?.find(
    (stat) => stat.type === StatTypes.TOTAL_SCORE,
  )?.value;

  const awayScore = awayMatchTeam?.outcome?.statistics?.find(
    (stat) => stat.type === StatTypes.TOTAL_SCORE,
  )?.value;

  let newUrlBase = urlBase;
  if (homeTeam) {
    const gradeName = seasonTeams[homeTeam.id]?.grade?.name ?? '';
    const gradeSlug = slugify(gradeName).toLowerCase();
    newUrlBase += `/${gradeSlug}`;
  }

  return (
    <MatchWidget
      awayLogoUrl={awayLogo}
      awaySquadName={awaySquadName}
      awaySquadPosition={awaySquadPosition}
      awaySquadScore={awayScore}
      borderClassName="border-0 px-4 pt-8 lg:px-2 lg:pt-6 xl:px-4 xl:pt-8"
      gameId={game.id}
      gameType={game.type}
      homeLogoUrl={homeLogo}
      homeSquadName={homeSquadName}
      homeSquadPosition={homeSquadPosition}
      homeSquadScore={homeScore}
      leagueImageUrl={leagueImageUrl}
      leagueName={leagueName}
      matchStatus={matchStatus}
      seasonId={seasonId}
      showBottomLogos
      showDisplayBoard={false}
      urlBase={newUrlBase}
      utcStartTime={utcStartTime}
      venueName={venueName}
    />
  );
}

function NavButtons({
  currentSlide,
  hasTakeoverAd,
  initialized,
  showNextArrow,
  showPrevArrow,
  sliderRef,
}: NavButtonsProps) {
  const NAV_BACKGROUND = (
    <svg fill="none" height="33" viewBox="0 0 43 43" width="33">
      <g filter="url(#filter0_d_2427_10098)">
        <circle cx="21.5" cy="20.5" fill="white" r="16.5" />
        <circle cx="21.5" cy="20.5" r="16" stroke="#D1D5DB" />
      </g>
      <defs>
        <filter
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse"
          height="43"
          id="filter0_d_2427_10098"
          width="43"
          x="0"
          y="0"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="2.5" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.11 0"
          />
          <feBlend
            in2="BackgroundImageFix"
            mode="normal"
            result="effect1_dropShadow_2427_10098"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_2427_10098"
            mode="normal"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );

  const handlePrevClick = () => {
    if (sliderRef.current) {
      sendToGtm({
        action: `page_num_${currentSlide - 1}`,
        label: 'local_matches_widget_left_click',
        trigger: 'local_matches_widget_nav_click_trigger',
        value: 'local_matches_widget_left_click',
      });
      sliderRef.current.slickPrev();
    }
  };

  const handleNextClick = () => {
    if (sliderRef.current) {
      sendToGtm({
        action: `page_num_${currentSlide + 1}`,
        label: 'local_matches_widget_right_click',
        trigger: 'local_matches_widget_nav_click_trigger',
        value: 'local_matches_widget_right_click',
      });
      sliderRef.current.slickNext();
    }
  };

  return (
    <div
      className={clsx(
        'hidden text-right text-xs font-medium lg:block',
        {
          'mx-2': !hasTakeoverAd,
        },
        {
          block: initialized,
          hidden: !initialized,
        },
      )}
    >
      <button
        className="cursor-pointer"
        disabled={!showPrevArrow}
        onClick={handlePrevClick}
        type="button"
      >
        {NAV_BACKGROUND}
        <FontAwesomeIcon
          className={clsx('relative -top-6 text-sm', {
            'text-gray-300': !showPrevArrow,
          })}
          icon={faChevronLeft}
        />
      </button>

      <button
        className="cursor-pointer"
        disabled={!showNextArrow}
        onClick={handleNextClick}
        type="button"
      >
        {NAV_BACKGROUND}
        <FontAwesomeIcon
          className={clsx('relative -top-6 text-sm', {
            'text-gray-300': !showNextArrow,
          })}
          icon={faChevronRight}
        />
      </button>
    </div>
  );
}

function LocalMatches({
  grade1Id,
  grade2Id,
  leagueImageId,
  leagueName,
  organisationId,
  seasonId,
  urlBase,
}: LocalMatchesProps): React.ReactElement | null {
  const [teamLogos, setTeamLogos] = useState<Record<string, LogoSizeMap>>({});
  const [fixture, setFixture] = useState<GradeFixtureResponse | null>(null);
  const [fixture2, setFixture2] = useState<GradeFixtureResponse | null>(null);
  const [seasonTeams, setSeasonTeams] = useState<Record<string, SeasonTeam>>(
    {},
  );
  const [playingSurfaces, setPlayingSurfaces] = useState<
    Record<string, PlayingSurface>
  >({});
  const [combinedGames, setCombinedGames] = useState<RoundGame[]>([]);

  const [currentSlide, setCurrentSlide] = useState(0);
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const [initialized, setInitialized] = useState(false);
  const sliderRef = useRef<Slider>(null);
  const responsiveType = useResponsiveTypeFromWidth();
  const slidesToScroll =
    responsiveType === ResponsiveType.TABLET_NARROW ? 3 : 1;
  const sportsHubSponsor = useAppSelector(
    (state) => state.features.sportsHubSponsor,
  );

  const resizeSlider = () => {
    let currentSlider = document.querySelector('.slick-list .slick-current');
    if (currentSlider) {
      let innerHeight = currentSlider.clientHeight;
      if (responsiveType === ResponsiveType.TABLET_NARROW) {
        for (let i = 0; i < slidesToScroll; i++) {
          currentSlider = currentSlider.nextSibling as Element;
          if (!currentSlider) {
            break;
          }
          innerHeight = Math.max(innerHeight, currentSlider.clientHeight);
        }
      }
      if (innerHeight) {
        (
          document.querySelector('.slick-list') as HTMLDivElement
        ).style.height = `${innerHeight + 5}px`;
      }
    }
  };

  useEffect(() => {
    if (!initialized) {
      setInitialized(true);
      setTimeout(() => {
        setTimeout(() => resizeSlider(), 50);
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialized]);

  useOnce(() => {
    if (seasonId) {
      fetchPlayHqSeasonTeamsAll(seasonId)
        .then((teams) => {
          if (teams) {
            const gradeIds = [grade1Id, grade2Id].filter(Boolean) as string[];
            const teamsForGrades = filterTeamsByGrade(gradeIds, teams);
            setSeasonTeams(mapTeamsById(teamsForGrades));
            setTeamLogos(mapLogosByTeamId(teamsForGrades));
          }
        })
        .catch(console.error);
      return true;
    }
    return false;
  }, [grade1Id, grade2Id, seasonId]);

  useOnce(() => {
    if (grade1Id) {
      fetchPlayHqGradeFixture(grade1Id)
        .then((res) => {
          if (res) {
            setFixture(res);
            if (res.playingSurfaces) {
              setPlayingSurfaces({
                ...playingSurfaces,
                ...mapPlayingSurfacesById(res.playingSurfaces),
              });
            }
            sendToGtm({
              label: 'local_matches_widget_impressions',
              trigger: 'local_matches_widget_impressions_trigger',
            });
          }
        })
        .catch(console.error);
      return true;
    }
    return false;
  }, [grade1Id, playingSurfaces]);

  useOnce(() => {
    if (grade2Id) {
      fetchPlayHqGradeFixture(grade2Id)
        .then((res) => {
          if (res) {
            setFixture2(res);
            if (res.playingSurfaces) {
              setPlayingSurfaces({
                ...playingSurfaces,
                ...mapPlayingSurfacesById(res.playingSurfaces),
              });
            }
          }
        })
        .catch(console.error);
      return true;
    }
    return false;
  }, [grade2Id, playingSurfaces]);

  useEffect(() => {
    let games: RoundGame[] = [
      ...(fixture?.rounds[getCurrentRoundIndex(fixture.rounds)]?.games ?? []),
      ...(fixture2?.rounds[getCurrentRoundIndex(fixture2.rounds)]?.games ??
        []),
    ];

    games = games.filter((game) => game.status === GameStatus.UPCOMING);
    games = games.sort(sortRoundGamesBySchedule);

    setCombinedGames(games);
  }, [fixture, fixture2]);

  const settings = {
    afterChange: () => {
      if (sportsHubSponsor.enabled && sportsHubSponsor.data) {
        const sponsorData =
          sportsHubSponsor.data.sponsorData[
            combinedGames[currentSlide].type ?? ''
          ];
        if (sponsorData) {
          setGtmDataLayer({
            data: {
              action: 'impression',
              section: 'upcoming_matches',
              type: sponsorData.name.toLowerCase(),
            },
            event: 'widget_sponsorship',
          });
        }
      }
      setTimeout(() => resizeSlider(), 50);
    },
    arrows: false,
    beforeChange: (_: number, next: number) => setCurrentSlide(next),
    dots: false,
    easing: 'easeInOut',
    infinite: false,
    responsive: [
      {
        breakpoint: 981,
        settings: {
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 767,
        settings: {
          slidesToScroll: 1,
        },
      },
    ],
    slidesToScroll: 1,
    slidesToShow: 1,
    speed: 300,
    variableWidth: true,
  };

  const showPrevArrow = currentSlide > 0;
  const showNextArrow =
    currentSlide + settings.slidesToShow < combinedGames.length;

  const leagueImageSize = 32;
  const leagueImageUrl =
    organisationId &&
    leagueImageId &&
    'https://res.cloudinary.com/playhq/image/upload/' +
      `h_${leagueImageSize},w_${leagueImageSize}/v1/production/afl/` +
      `${organisationId}/${leagueImageId}/logo.png`;

  if (!initialized || !combinedGames.length) {
    return null;
  }

  return (
    <div className="flex flex-col">
      <div className="grid place-content-between text-2xl font-extrabold text-slate-750 lg:grid-cols-3 lg:text-xl xl:grid-cols-4 xl:text-2xl">
        <h2 className="pb-4 lg:col-span-2 xl:col-span-3">
          Local Footy
          <FontAwesomeIcon
            className="ml-2 text-lg lg:hidden"
            icon={faChevronRight}
          />
        </h2>
        {sliderRef && (
          <NavButtons
            currentSlide={currentSlide}
            hasTakeoverAd={hasTakeoverAd}
            initialized={initialized}
            showNextArrow={showNextArrow}
            showPrevArrow={showPrevArrow}
            sliderRef={sliderRef}
          />
        )}
      </div>
      <div
        className={clsx({
          flex: initialized,
          hidden: !initialized,
        })}
      >
        <div className={clsx('mb-4 w-full', styles.slickWrapper)}>
          <Slider
            className={clsx('flex flex-row', viewType, {
              'takeover-ad': hasTakeoverAd,
            })}
            ref={sliderRef}
            /* eslint-disable-next-line react/jsx-props-no-spreading */
            {...settings}
          >
            {combinedGames.map((game) => (
              <div
                className="rounded-md border-1 border-gray-300 hover:border-green-550"
                key={game.id}
              >
                <GameWidget
                  game={game}
                  leagueImageUrl={leagueImageUrl}
                  leagueName={leagueName}
                  playingSurfaces={playingSurfaces}
                  seasonId={seasonId}
                  seasonTeams={seasonTeams}
                  teamLogos={teamLogos}
                  urlBase={urlBase}
                />
              </div>
            ))}
          </Slider>
        </div>
      </div>
    </div>
  );
}
export default LocalMatches;
