import clsx from 'clsx';
import React, { useMemo } from 'react';

import { useAppSelector } from 'store/hooks';
import Ad from 'themes/autumn/components/ads/Ad';
import Teads from 'themes/autumn/components/ads/Teads';
import Signpost from 'themes/autumn/components/stories/Signpost';
import processExploreTravelInjection from 'themes/autumn/templates/stories/ExploreTravelStory/utils';
import TextToSpeech from 'themes/autumn/templates/stories/common/TextToSpeech';
import {
  applyPaywallHiding,
  shouldApplyPaywall,
} from 'themes/autumn/utils/paywallUtils';
import { MonacoElementType, StoryElementType } from 'types/Story';
import { StoryViewType } from 'types/ZoneItems';
import { AdSize } from 'util/ads';
import { DeviceType } from 'util/device';
import { useDeviceTypeFromWidth, useLazyLoadComponentState } from 'util/hooks';
import { isSponsoredPage } from 'util/page';
import { getItemsShownBehindPaywall } from 'util/piano';

import BusinessFeatureAdsUnit from '../../../templates/zoneItems/classified/BusinessFeatureAdsUnit';
import PianoPaywall from '../../generic/PianoPaywall';
import {
  type StoryComponentMap,
  defaultStoryComponentMap,
} from '../../storyElements/storyElementsMap';
import ViafouraConversationStarter from '../Comments/ViafouraConversationStarter';
import { ColorPalette } from '../Signpost/constants';
import ByLine from '../StoryPageHeadline/ByLine';
import MugshotContainer from '../StoryPageHeadline/MugshotsContainer';

import StoryComponentWrapper from './StoryComponentWrapper';
import { StoryComponentWrapperMaxWidthEnum } from './enums';

import type {
  Props as AdProps,
  AdSizeType,
} from 'themes/autumn/components/ads/Ad';
import type {
  BusinessFeatureAds,
  Story,
  StoryAuthor,
  StoryElement,
} from 'types/Story';
import type { Heights } from 'util/device';

const DEFAULT_HEIGHT_BEFORE_AD: Heights = {
  [DeviceType.DESKTOP]: 800,
  [DeviceType.MOBILE]: 600,
  [DeviceType.TABLET]: 700,
};

const DEFAULT_AD_SIZE: AdSizeType = AdSize.mrec;
const DEFAULT_AD_LG_SIZE: AdSizeType = [AdSize.mrec, AdSize.leaderboard];
const DEFAULT_AD_MD_SIZE: AdSizeType = AdSize.mrec;

const elementsToHideFromPaywallPreview = new Set([StoryElementType.Listbox]);

export interface Props {
  adFrequency?: number;
  adLgSizes?: AdSizeType;
  adMdSizes?: AdSizeType;
  adSizes?: AdSizeType;
  adTriggerHeight?: Heights;
  authors?: StoryAuthor[];
  businessFeatureAds?: BusinessFeatureAds;
  byline?: string | null;
  classNameForPaywall?: string;
  classNameForStoryComponentWrapper?: string;
  customStoryComponentMap?: StoryComponentMap;
  elements?: StoryElement[];
  hideLeadingImage?: boolean;
  hideRecommendation?: boolean;
  injectComponents?: Array<{
    injectComponent: React.ReactNode;
    injectComponentClassName?: string;
    injectPos?: number;
    skipElementsAfterInjection?: boolean;
  }>;
  isTextToSpeechEnabled?: boolean;
  noMaxWidthForStoryElements?: boolean;
  publishingTimes?: React.ReactNode;
  showAds?: boolean;
  showByAuthor?: boolean;
  showBylineSignpost?: boolean;
  showConversationStarter?: boolean;
  textToSpeechProjectId?: string;
  usePaywall?: boolean;
}

interface AuthorProps {
  authors: StoryAuthor[];
  byline: string | null;
  classNameForStoryComponentWrapper?: string;
  publishingTimes: React.ReactNode;
  showBylineSignpost: boolean;
  story: Story;
}

function Author({
  authors,
  byline,
  classNameForStoryComponentWrapper,
  publishingTimes,
  showBylineSignpost,
  story,
}: AuthorProps) {
  return (
    <StoryComponentWrapper
      className={classNameForStoryComponentWrapper}
      maxWidth={StoryComponentWrapperMaxWidthEnum.None}
      type={StoryElementType.Generic}
    >
      <div className="mb-0 flex w-full flex-row items-center justify-start md:mb-6 md:w-180">
        <MugshotContainer authors={authors ?? []} />
        <ByLine
          authorNameClassName="underline text-gray-900"
          authors={authors ?? []}
          byline={byline}
          publishingTimes={publishingTimes}
          textSizeClassName="text-base"
        />
      </div>
      {showBylineSignpost && (
        <Signpost
          colorPalette={ColorPalette.PALETTE_EXPLORE}
          // eslint-disable-next-line @stylistic/max-len
          containerClassName="md:hidden flex flex-col justify-center"
          publishFrom={story.publishFrom}
          tags={story.tags}
        />
      )}
    </StoryComponentWrapper>
  );
}

function checkAdTriggers(
  deviceType: DeviceType,
  heightSoFar: number,
  overrides?: Heights,
): boolean {
  return (
    heightSoFar >
    (overrides?.[deviceType] || DEFAULT_HEIGHT_BEFORE_AD[deviceType])
  );
}

function addElementWrapperHeights(height: number): number {
  const wrapperHeight = 24; // For: margin-bottom
  return height ? height + wrapperHeight : height;
}

function WrappedAd({
  lgSizes = [AdSize.mrec, AdSize.leaderboard],
  position,
  sizes = AdSize.mrec,
  ...props
}: Omit<AdProps, 'publiftName' | 'slotId'>) {
  const { showComponentPlaceholder } = useLazyLoadComponentState();
  const isStoryTravel = useAppSelector(
    (state) =>
      (state.settings.viewType as StoryViewType) ===
      StoryViewType.STORY_TRAVEL,
  );

  if (!showComponentPlaceholder) {
    return null;
  }

  const publiftName = isStoryTravel
    ? 'incontent-hrec-lg-1'
    : `incontent-mrec-${Math.min(4, position - 2)}`;

  return (
    <div className="mx-auto mb-6">
      <Ad
        // eslint-disable-next-line react/jsx-props-no-spreading
        {...props}
        className="mt-6"
        lgSizes={lgSizes}
        position={position}
        // After the 4th one, repeat as many times as needed
        publiftName={publiftName}
        sizes={sizes}
        slotId={`story${position}`}
      />
    </div>
  );
}

function StoryElements({
  adFrequency = 25,
  adLgSizes = DEFAULT_AD_LG_SIZE,
  adMdSizes = DEFAULT_AD_MD_SIZE,
  adSizes = DEFAULT_AD_SIZE,
  adTriggerHeight = DEFAULT_HEIGHT_BEFORE_AD,
  authors,
  businessFeatureAds,
  byline = null,
  classNameForPaywall,
  classNameForStoryComponentWrapper,
  customStoryComponentMap,
  elements,
  hideLeadingImage = false,
  hideRecommendation = false,
  injectComponents,
  isTextToSpeechEnabled,
  noMaxWidthForStoryElements,
  publishingTimes,
  showAds = false,
  showByAuthor,
  showBylineSignpost = false,
  showConversationStarter = true,
  textToSpeechProjectId,
  usePaywall = false,
}: Props): React.ReactElement | null {
  const storyComponentMap: StoryComponentMap = useMemo(
    () => customStoryComponentMap || defaultStoryComponentMap,
    [customStoryComponentMap],
  );

  const availableElements: StoryElement[] = useMemo(() => {
    if (!elements) {
      return [];
    }

    return elements.filter(
      // Since the component map is a tuple of [Component, EstimateHeightFn],
      // we check if the map has a component for the element type.
      (element) => !!storyComponentMap[element.type]?.[0],
    );
  }, [storyComponentMap, elements]);

  const leadingImageIndex = useMemo(
    () =>
      availableElements?.findIndex(
        (element) => element.type === StoryElementType.Image,
      ) ?? -1,
    [availableElements],
  );
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const pianoUser = useAppSelector((state) => state.piano.user);
  const teadsFeature = useAppSelector((state) => state.features.teads);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const loadingPaywall = useAppSelector((state) => state.piano.loadingPaywall);
  const hasPaywall = useAppSelector((state) => state.piano.hasPaywall);
  const registrationOnly = useAppSelector(
    (state) =>
      state.features.piano.enabled &&
      state.features.piano.data.registrationOnly,
  );
  const adServingEnabled = useAppSelector(
    (state) => state.features.adServing.enabled,
  );
  let hasPianoPaywall = loadingPaywall || hasPaywall;
  if (registrationOnly && pianoUser) {
    hasPianoPaywall = false;
  }

  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  const deviceType = useDeviceTypeFromWidth();
  const teadsAllowed = teadsFeature.enabled && !isSponsoredPage(viewType);

  let adEligibleIndex = -1; // Ensure it starts at 0 once counting starts

  const INITIAL_AD_COUNT = adFrequency >= 2 ? 2 : adFrequency;
  const AD_MAX_COUNT_IN_BODY = adFrequency;
  const AD_COUNT_FOR_TEADS = 4;

  const itemsShownBehindPaywall =
    getItemsShownBehindPaywall(availableElements);

  const story = useAppSelector((state) => state.story);

  if (availableElements.length > 0 && hideLeadingImage) {
    if (leadingImageIndex !== -1) {
      // minus adEligibleIndex by 1 when hiding the leading image
      adEligibleIndex = -2;
    }
  }

  const hasLeadImage = leadingImageIndex !== -1 && !hideLeadingImage;
  const authorIndex = hasLeadImage ? 1 : 0;

  // ensure to send the ads request after paywall loading
  const adsEnabled =
    pianoFeature.enabled && usePaywall
      ? adServingEnabled && !hasPianoPaywall
      : adServingEnabled;

  let heightSoFar: number = 0;
  let teadsAdDone: boolean = false;
  let adCount: number = INITIAL_AD_COUNT;

  // Sort by position to ensure they're applied in the correct order
  const sortedInjectComponents = useMemo(() => {
    if (!injectComponents) return [];
    return [...injectComponents].sort(
      (a, b) => (a.injectPos ?? 0) - (b.injectPos ?? 0),
    );
  }, [injectComponents]);

  const authorComponent = (
    <Author
      authors={authors ?? []}
      byline={byline ?? ''}
      classNameForStoryComponentWrapper={classNameForStoryComponentWrapper}
      publishingTimes={publishingTimes}
      showBylineSignpost={showBylineSignpost}
      story={story}
    />
  );

  return (
    <>
      {availableElements &&
        availableElements.map((element, index) => {
          // Skip rendering elements after the injection point if true
          const paywall = shouldApplyPaywall(
            pianoFeature,
            usePaywall,
            hasPianoPaywall,
          );
          if (
            !paywall &&
            injectComponents?.some(
              (injection) =>
                injection.injectPos !== undefined &&
                injection.injectPos < index &&
                injection.skipElementsAfterInjection !== false,
            )
          ) {
            return null;
          }

          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const StoryComponent: React.ComponentType<any> | undefined =
            storyComponentMap[element.type]?.[0];

          if (!StoryComponent) {
            return null;
          }

          let component =
            hideLeadingImage && index === leadingImageIndex ? null : (
              <>
                <StoryComponentWrapper
                  className={classNameForStoryComponentWrapper}
                  maxWidth={
                    noMaxWidthForStoryElements
                      ? StoryComponentWrapperMaxWidthEnum.None
                      : undefined
                  }
                  type={element.type}
                >
                  <StoryComponent element={element} index={index} />
                </StoryComponentWrapper>
                {showConversationStarter &&
                index === Math.floor(availableElements.length / 2) ? (
                  <ViafouraConversationStarter />
                ) : null}
              </>
            );

          const injectComponentToAdd = sortedInjectComponents.find(
            (injection) => injection.injectPos === index,
          );

          if (!paywall && injectComponentToAdd) {
            return (
              <>
                {showByAuthor && index === authorIndex && authorComponent}
                {processExploreTravelInjection({
                  applyPaywallHiding,
                  classNameForStoryComponentWrapper,
                  component,
                  elements: availableElements,
                  hasPaywall,
                  hasPianoPaywall,
                  index,
                  injectComponentToAdd,
                  itemsShownBehindPaywall,
                  noMaxWidthForStoryElements,
                  pianoFeature,
                  shouldApplyPaywall,
                  showConversationStarter,
                  storyComponentMap,
                  usePaywall,
                })}
              </>
            );
          }

          if (paywall) {
            if (elementsToHideFromPaywallPreview.has(element.type)) {
              component = <div className="hidden">{component}</div>;
              return component;
            }

            if (index === itemsShownBehindPaywall.fadeIdx) {
              component = (
                <>
                  <div
                    className={clsx('gradient-mask-b-0', {
                      'mx-auto': noMaxWidthForStoryElements,
                    })}
                  >
                    {component}
                  </div>
                  <StoryComponentWrapper
                    className={classNameForStoryComponentWrapper}
                    maxWidth={
                      noMaxWidthForStoryElements
                        ? StoryComponentWrapperMaxWidthEnum.None
                        : undefined
                    }
                    type={StoryElementType.Generic}
                  >
                    <PianoPaywall className={classNameForPaywall} />
                  </StoryComponentWrapper>
                </>
              );
            } else {
              component = applyPaywallHiding(
                component,
                index,
                itemsShownBehindPaywall,
                hasPaywall,
              );
            }
          }

          // Ignore headings when counting elements for ad placements

          const adAllowedAfterElementType =
            element.type !== StoryElementType.Heading;

          const elementAllowsAds =
            isClientSide && adsEnabled && showAds && adAllowedAfterElementType;

          let triggeredAd: React.ReactElement | null = null;

          if (
            isClientSide &&
            adsEnabled &&
            showAds &&
            adCount <= AD_MAX_COUNT_IN_BODY
          ) {
            const estimateHeightFn = storyComponentMap[element.type]?.[1];
            if (estimateHeightFn) {
              const elementHeight = addElementWrapperHeights(
                estimateHeightFn({
                  deviceType,
                  item: {
                    element,
                    index,
                  },
                }),
              );

              heightSoFar += elementHeight;

              const adTriggered = checkAdTriggers(
                deviceType,
                heightSoFar,
                adTriggerHeight,
              );

              if (adAllowedAfterElementType) {
                if (adTriggered) {
                  adCount += 1;
                  heightSoFar = 0;
                  const isTeadsAd =
                    teadsAllowed && adCount === AD_COUNT_FOR_TEADS;

                  if (isTeadsAd) {
                    teadsAdDone = true;
                  }

                  triggeredAd = (
                    <>
                      {isTeadsAd ? (
                        <Teads />
                      ) : (
                        <WrappedAd
                          // HACK: Seems maybe a bug googletag library not
                          // working specifically for 1280 screen size, so
                          // using LG sizes for desktop.
                          lgSizes={adLgSizes}
                          mdSizes={adMdSizes}
                          position={
                            teadsAllowed && teadsAdDone ? adCount - 1 : adCount
                          }
                          sizes={adSizes}
                          // HACK: See above
                          // xlSizes={
                          //   deviceType === DeviceType.DESKTOP
                          //     ? [AdSize.mrec, AdSize.leaderboard]
                          //     : []
                          // }
                        />
                      )}
                    </>
                  );
                }
              }
            }
          }

          if (elementAllowsAds) {
            adEligibleIndex += 1;
          }

          return (
            // eslint-disable-next-line react/no-array-index-key
            <React.Fragment key={index}>
              {businessFeatureAds?.enable && adEligibleIndex === 4 && (
                <BusinessFeatureAdsUnit
                  titleTag={businessFeatureAds.titleTag}
                />
              )}
              {showByAuthor && index === authorIndex && authorComponent}
              {index === 1 &&
                isTextToSpeechEnabled &&
                textToSpeechProjectId && (
                  <StoryComponentWrapper
                    className={classNameForStoryComponentWrapper}
                    maxWidth={StoryComponentWrapperMaxWidthEnum.None}
                    type={MonacoElementType.TextToSpeech}
                  >
                    <TextToSpeech projectId={textToSpeechProjectId} />
                  </StoryComponentWrapper>
                )}
              {component}
              {triggeredAd}
            </React.Fragment>
          );
        })}
      {pianoFeature.enabled && !hasPianoPaywall && !hideRecommendation && (
        <>
          <div className="min-h-px" id="recommendationEndArticle" />
          <div id="endPromoArticle" />
        </>
      )}
    </>
  );
}

export default StoryElements;
