import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import Ad from 'themes/autumn/components/ads/Ad';
import PolarOrScrollX from 'themes/autumn/components/ads/PolarOrScrollX';
import { HomePageFeatures } from 'themes/autumn/components/features';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import Zone from 'themes/autumn/components/zone/Zone';
import LocalAdUnitBase from 'themes/autumn/templates/zoneItems/classified/LocalAdUnit/LocalAdUnitBase';
import OwnLocalAds from 'themes/autumn/templates/zoneItems/classified/OwnLocalAds';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { WidgetPosition } from 'types/businessProfiles';
import { AD_SIDEBAR_STYLE, AdSize } from 'util/ads';
import { useLazyLoadComponentState } from 'util/hooks';

import type { Props as ZoneProps } from 'themes/autumn/components/zone/Zone';

const insertBreakpointSidebar: ZoneProps['insertItemsAfter'] = (
  zoneItem,
  _,
  items,
): React.ReactNode | null => {
  const insertAt = items.find(
    (i) => i.zoneItemType === ZoneItemType.StoryList,
  );
  if (!insertAt || zoneItem.zoneItemId !== insertAt.zoneItemId) return null;
  return (
    <div className="flex flex-col gap-6 lg:hidden">
      <Zone name={ZoneName.MAIN_SIDE} />
    </div>
  );
};

const insertPolarOrScrollX: ZoneProps['insertItemsAfter'] = (
  zoneItem,
  _,
  items,
) => {
  const insertAt = items.find(
    (i) => i.zoneItemType === ZoneItemType.StoryList,
  );
  if (!insertAt || zoneItem.zoneItemId !== insertAt.zoneItemId) return null;
  return <PolarOrScrollX />;
};

function Index(): React.ReactElement {
  const hasGutterAd = useAppSelector((state) => state.page.hasGutterAd);
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const businessProfilesFeature = useAppSelector(
    (state) => state.features.businessProfiles,
  );
  const hasDirectory = useAppSelector(
    (state) =>
      state.features.ownLocal.enabled &&
      state.features.ownLocal.data.hasDirectory,
  );
  const lazyLoadAds = useLazyLoadComponentState(true);

  return (
    <TemplateWrapper showRevBanner showStickyFooterAd>
      <div className="font-inter">
        <Container className="md:mt-4" noGutter>
          <div
            className={clsx('mx-4 md:mx-6', {
              'lg:flex': !hasGutterAd,
              'lg:mx-32 lg:px-11': hasGutterAd,
              'xl:ml-0': hasTakeoverAd,
              'xl:mx-0': !hasTakeoverAd,
            })}
          >
            <div
              className={clsx('w-full', {
                'lg:w-7/10': !hasGutterAd,
              })}
            >
              <div
                className={clsx('border-gray-300', {
                  'lg:pr-7': !hasGutterAd,
                })}
                data-testid="index-newswell"
              >
                <Zone
                  insertItemsAfter={
                    lazyLoadAds.showComponent
                      ? insertPolarOrScrollX
                      : undefined
                  }
                  name={ZoneName.NEWSWELL}
                />
              </div>
            </div>
            <div
              className={clsx({
                'lg:w-3/10 lg:min-w-side lg:border-l-1 lg:pl-7': !hasGutterAd,
              })}
            >
              <div className="top-20 z-10 flex flex-col justify-items-stretch gap-6 lg:sticky lg:gap-10">
                <Zone
                  editOnlyItems={[0, 1]}
                  insertItemsBefore={{
                    0: (
                      <Ad
                        // eslint-disable-next-line react/jsx-props-no-spreading
                        {...AD_SIDEBAR_STYLE}
                        lgSizes={{
                          defaultPlaceholderSize: AdSize.mrec,
                          sizes: [AdSize.halfPage, AdSize.mrec],
                        }}
                        position={1}
                        publiftName="side-1"
                        sizes={[]}
                        slotId="newswell-side-1"
                      />
                    ),
                  }}
                  name={ZoneName.NEWSWELL_SIDE}
                />
              </div>
            </div>
          </div>
        </Container>
        <Ad
          className="my-8"
          labelPosition="top"
          labelSpace="space-y-2"
          lgSizes={AdSize.leaderboard}
          placeholderPadding="pb-7 pt-3"
          position={2}
          publiftName="incontent-hrec-1"
          sizes={[]}
          slotId="newswell-leaderboard"
          supportPortal
        />
        <Container className="md:mt-6 lg:mt-0 lg:flex">
          <div className="w-full lg:w-7/10">
            <div className="border-gray-300 lg:pr-7" data-testid="index-main">
              <Zone
                insertItemsAfter={insertBreakpointSidebar}
                name={ZoneName.MAIN}
              />
            </div>
            {businessProfilesFeature.enabled &&
              businessProfilesFeature.data.widgetPosition ===
                WidgetPosition.Bottom &&
              (hasDirectory ? (
                <OwnLocalAds limit={3} />
              ) : (
                <div className="my-4 empty:hidden lg:my-0 lg:pr-7">
                  <LocalAdUnitBase
                    carouselBreakpoint="block"
                    showHeading
                    verticalStackBreakpoint="hidden"
                  />
                </div>
              ))}
          </div>
          <div className="hidden gap-6 lg:flex lg:w-3/10 lg:min-w-side lg:flex-col lg:gap-10 lg:border-l-1 lg:pl-7">
            <Zone name={ZoneName.MAIN_SIDE} />
            <Ad
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...AD_SIDEBAR_STYLE}
              lgSizes={{
                defaultPlaceholderSize: AdSize.mrec,
                sizes: [AdSize.halfPage, AdSize.mrec],
              }}
              position={3}
              publiftName="home-side-2"
              sizes={[]}
              slotId="main-side"
              sticky="top-20"
            />
          </div>
        </Container>
        <HomePageFeatures />
      </div>
    </TemplateWrapper>
  );
}

export default Index;
