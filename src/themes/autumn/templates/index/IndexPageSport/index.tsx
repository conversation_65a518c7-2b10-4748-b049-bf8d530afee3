import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import Ad from 'themes/autumn/components/ads/Ad';
import { IndexPageFeatures } from 'themes/autumn/components/features';
import Container from 'themes/autumn/components/generic/Container';
import SportSponsorBanner from 'themes/autumn/components/generic/SportSponsorBanner';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import PageHeading from 'themes/autumn/components/page/PageHeading';
import PageNavigation from 'themes/autumn/components/page/PageNavigation';
import Zone from 'themes/autumn/components/zone/Zone';
import BonzaiAwareAd from 'themes/autumn/templates/zoneItems/advertisement/BonzaiAwareAd';
import Breadcrumb from 'themes/autumn/templates/zoneItems/navigation/common/elements/Breadcrumb';
import { NavigationType } from 'types/Nav';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { AD_SIDEBAR_STYLE, AdSize } from 'util/ads';
import {
  useLazyLoadComponentState,
  usePageParents,
  usePageZoneItems,
  usePages,
} from 'util/hooks';

import { SPORT_NAV_THEME } from './theme';

import type { Props as ZoneProps } from 'themes/autumn/components/zone/Zone';

interface Props {
  position: number;
}

function LeaderBoardAd({ position }: Props): React.ReactElement | null {
  const { showComponentPlaceholder } = useLazyLoadComponentState();
  if (!showComponentPlaceholder) return null;

  return (
    <div className="my-6">
      <BonzaiAwareAd
        mdSizes={AdSize.leaderboard}
        mdSizesWithBonzai={AdSize.leaderboard}
        position={position}
        publiftName={`incontent-hrec-${Math.min(4, position)}`}
        sizes={[]}
        slotId={`newswell-leaderboard-${position}`}
      />
    </div>
  );
}

function IndexPageSport(): React.ReactElement {
  const pages = usePages();
  const page = useAppSelector((state) => state.page);
  const { parent: parentPage } = usePageParents();
  const hasGutterAd = useAppSelector((state) => state.page.hasGutterAd);
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const newswellSideZoneItems = usePageZoneItems(ZoneName.NEWSWELL_SIDE);
  const newswellZoneItems = usePageZoneItems(ZoneName.NEWSWELL);
  const mainZoneItems = usePageZoneItems(ZoneName.MAIN);
  const mainSideZoneItems = usePageZoneItems(ZoneName.MAIN_SIDE);
  const showHeading = useAppSelector((state) => state.page.showHeading);
  const newsWellSideAds = (
    <Ad
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...AD_SIDEBAR_STYLE}
      lgSizes={{
        defaultPlaceholderSize: AdSize.mrec,
        sizes: [AdSize.halfPage, AdSize.mrec],
      }}
      position={1}
      publiftName="side-1"
      sizes={AdSize.mrec}
      slotId="newswell-side-1"
      sticky="top-20"
    />
  );

  const mainSideAds = (
    <Ad
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...AD_SIDEBAR_STYLE}
      lgSizes={{
        defaultPlaceholderSize: AdSize.mrec,
        sizes: [AdSize.halfPage, AdSize.mrec],
      }}
      position={1}
      publiftName="side-2"
      sizes={AdSize.mrec}
      slotId="main-side-1"
      sticky="top-20"
    />
  );

  const newsWellAdsAfter: ZoneProps['insertItemsAfter'] = (
    zoneItem,
    i,
    items,
  ) =>
    items.findIndex((item) => item.zoneItemType === ZoneItemType.StoryList) ===
    i ? (
      <LeaderBoardAd position={2} />
    ) : null;

  const mainSideAdsAfter: ZoneProps['insertItemsAfter'] = (
    zoneItem,
    i,
    items,
  ) =>
    (mainZoneItems.length > 0 || mainSideZoneItems.length > 0) &&
    (i === items.length - 1 ? mainSideAds : null);

  const newsWellSideAdsAfter: ZoneProps['insertItemsAfter'] = (
    zoneItem,
    i,
    items,
  ) =>
    (newswellSideZoneItems.length > 0 || newswellZoneItems.length > 0) &&
    (i === items.length - 1 ? newsWellSideAds : null);

  return (
    <TemplateWrapper showNavigationAd showRevBanner showStickyFooterAd>
      <Container className="mt-4 md:mt-10" noGutter>
        {parentPage?.name && (
          <Breadcrumb
            breadcrumbEntryClassName="text-sm font-medium"
            className="mb-3 ml-3 overflow-hidden focus:block group-focus-within:opacity-0 lg:hidden"
          />
        )}
        <div className="ml-4 overflow-y-visible md:mx-6 xl:mx-0">
          <PageHeading show={showHeading} text={page.name} />
          <PageNavigation
            desktopMoreOptionEnabled
            fontStyle="text-sm font-medium normal-case"
            mobileMoreOptionEnabled={false}
            navClassName=""
            // eslint-disable-next-line @stylistic/max-len
            navWrapperClassName="md:border-b-1 border-gray-300 pb-2 md:pb-6 md:mb-10"
            navigationType={NavigationType.Pill}
            pages={pages}
            theme={SPORT_NAV_THEME}
          />
        </div>
      </Container>
      <Container className="md:mt-10" noGutter>
        <div
          className={clsx('mx-4 md:mx-6', {
            'lg:flex': !hasGutterAd,
            'lg:mx-32 lg:px-11': hasGutterAd,
            'xl:ml-0': hasTakeoverAd,
            'xl:mx-0': !hasTakeoverAd,
          })}
        >
          <div
            className={clsx('w-full', {
              'lg:w-7/10': !hasGutterAd,
            })}
          >
            <SportSponsorBanner
              className={clsx(showHeading && 'mt-6 md:mb-4')}
            />
            <div
              className={clsx('border-gray-300', {
                'lg:pr-7': !hasGutterAd,
              })}
              data-testid="index-newswell"
            >
              <Zone
                insertItemsAfter={newsWellAdsAfter}
                name={ZoneName.NEWSWELL}
              />
            </div>
          </div>
          <div
            className={clsx(
              'flex flex-col justify-items-stretch gap-6 lg:gap-10',
              {
                'lg:w-3/10 lg:min-w-side lg:pl-7': !hasGutterAd,
              },
            )}
          >
            <Zone
              insertItemsAfter={newsWellSideAdsAfter}
              name={ZoneName.NEWSWELL_SIDE}
            />
            {newswellZoneItems.length > 0 &&
              newswellSideZoneItems.length === 0 &&
              newsWellSideAds}
          </div>
        </div>
      </Container>
      <Container className="mt-6 lg:flex">
        <div className="w-full lg:w-7/10">
          <div className="border-gray-300 lg:pr-7" data-testid="index-main">
            <Zone name={ZoneName.MAIN} />
          </div>
        </div>
        <div className="mt-6 flex flex-col gap-6 md:mt-0 lg:w-3/10 lg:min-w-side lg:gap-10 lg:pl-7">
          <Zone
            insertItemsAfter={mainSideAdsAfter}
            name={ZoneName.MAIN_SIDE}
          />
          {mainZoneItems.length > 0 &&
            mainSideZoneItems.length === 0 &&
            mainSideAds}
        </div>
      </Container>
      <IndexPageFeatures />
    </TemplateWrapper>
  );
}

export default IndexPageSport;
