// Jest <PERSON>napshot v1, https://jestjs.io/docs/snapshot-testing

exports[`ags property story render with farmbuy logo 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"NewsArticle","description":"","isPartOf":{"@type":["CreativeWork","Product"],"name":"","productID":"/subscribe/"},"keywords":"property,rural","mainEntityOfPage":{"@type":"WebPage","@id":"https://resi.uatz.view.com.au/advice/selling/clean-code-vs-dirty-code-react-best-practices/"},"headline":"test title 0","image":[],"articleSection":"","dateCreated":"2021-06-30T00:27:33.248683+00:00","datePublished":"2021-06-30T00:27:33.248683+00:00","dateModified":"2021-06-30T00:27:33.248683+00:00","author":{"@type":"Person","name":"<PERSON>de"},"articleBody":"","isAccessibleForFree":false}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"},{"@type":"ListItem","position":2,"item":"https://story/100000","name":"test title 0"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-sm md:max-w-lg md:px-6 xl:px-0"
    >
      <div
        class="mb-8 mt-4 space-y-4 md:mt-7 md:space-y-7 lg:mt-14"
      >
        <div
          class="mx-auto flex flex-row px-4 empty:hidden md:px-0"
        />
        <div
          class="px-4 md:px-0 border-gray-900 lg:border-b"
        >
          <h1
            class="mb-5 text-heading-mobile font-bold text-gray-900 md:text-4xl md:leading-12 lg:mb-7 font-merriweather"
            data-testid="story-title"
          >
            test title 0
          </h1>
          <div
            class="mb-7 flex h-auto flex-row flex-wrap items-center lg:h-8 lg:flex-nowrap md:flex-nowrap"
          >
            <div
              class="mb-6 flex size-full shrink-0 flex-row items-center lg:mb-0 lg:w-auto md:mb-0 md:w-auto"
            >
              <div
                class="h-full items-center justify-center font-inter font-normal lg:pr-4 text-xs hidden md:block lg:pr-6 md:pr-4 flex flex-col"
              >
                <div
                  class=""
                >
                  <div
                    class="relative -top-1"
                  >
                    <span>
                      By
                    </span>
                     
                    <span>
                      Callum Godde
                    </span>
                  </div>
                  <div
                    class="leading-4"
                  >
                    <span>
                      June 30 2021 - 12:27am
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="h-full items-center justify-center font-inter font-normal lg:pr-4 text-xs border-gray-400 md:hidden lg:border-r md:mr-4 md:border-r md:pr-4 flex flex-col"
              >
                <div
                  class=""
                >
                  <div
                    class="relative -top-1"
                  >
                    <span>
                      By
                    </span>
                     
                    <span>
                      Callum Godde
                    </span>
                  </div>
                  <div
                    class="leading-4"
                  >
                    <span>
                      June 30 2021 - 12:27am
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex size-full flex-row flex-wrap items-center space-x-1 divide-x-1 divide-gray-200 border-y border-gray-200 py-2 md:divide-none md:border-y-0 md:border-l lg:flex-nowrap"
            >
              <div
                class="relative"
                data-headlessui-state=""
              >
                <button
                  aria-expanded="false"
                  aria-label="Share"
                  class="flex h-9 w-[94px] cursor-pointer flex-row items-center justify-center gap-1 hover:bg-gray-100 focus-visible:outline-none ml-1 rounded-none md:rounded-3xl"
                  data-headlessui-state=""
                  id="headlessui-popover-button-:r2:"
                  type="button"
                >
                  <svg
                    fill="none"
                    height="20"
                    viewBox="0 0 20 20"
                    width="20"
                  >
                    <path
                      class="fill-gray-900"
                      d="M19 9.914 11.086 2v5.185a9.53 9.53 0 0 0-6.274 2.798A9.537 9.537 0 0 0 2 16.771v2.007l.965-1.76a8.577 8.577 0 0 1 8.12-4.428v5.238L19 9.914Zm-8.512 1.626a9.61 9.61 0 0 0-7.236 3.283C4.136 11.033 7.544 8.2 11.6 8.2h.514V4.483l5.431 5.431-5.43 5.431v-3.677l-.452-.055a9.59 9.59 0 0 0-1.175-.073Z"
                    />
                  </svg>
                  <span
                    class="text-xs"
                  >
                    Share
                  </span>
                </button>
                <div
                  class="absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden"
                />
              </div>
              <div />
            </div>
            <div
              class="order-first flex grow justify-end md:order-last"
            >
              <a
                class="gtm-hook-farmbuy-article flex gap-x-3 self-center"
                href="https://www.farmbuy.com/"
                rel="noopener"
                target="_blank"
              >
                <picture>
                  <img
                    alt="FarmBuy Real Estate"
                    class="inline h-8 md:h-auto"
                    loading="lazy"
                    src="https://cdn.newsnow.io/117776977/fece66dd-3f4e-4402-a018-ecae51204964.svg"
                  />
                </picture>
              </a>
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex-col md:flex-row lg:flex"
      >
        <div
          class="w-full md:relative lg:w-7/10 lg:pr-9 xl:pr-10 font-merriweather"
          id="story-body"
        >
          <div
            class="relative mx-auto my-14 px-4 md:my-20 md:px-0"
            id="story-footer"
          >
            <div
              class="invisible relative -top-12 block md:-top-16"
            />
            <div
              class="flex items-center justify-start space-x-1 divide-x divide-gray-200 border-y border-gray-200 py-2 font-inter md:py-4 mb-6"
            >
              <div
                class="relative"
                data-headlessui-state=""
              >
                <button
                  aria-expanded="false"
                  aria-label="Share"
                  class="flex h-9 w-[94px] cursor-pointer flex-row items-center justify-center gap-1 hover:bg-gray-100 focus-visible:outline-none ml-1 rounded-none md:rounded-3xl"
                  data-headlessui-state=""
                  id="headlessui-popover-button-:r8:"
                  type="button"
                >
                  <svg
                    fill="none"
                    height="20"
                    viewBox="0 0 20 20"
                    width="20"
                  >
                    <path
                      class="fill-gray-900"
                      d="M19 9.914 11.086 2v5.185a9.53 9.53 0 0 0-6.274 2.798A9.537 9.537 0 0 0 2 16.771v2.007l.965-1.76a8.577 8.577 0 0 1 8.12-4.428v5.238L19 9.914Zm-8.512 1.626a9.61 9.61 0 0 0-7.236 3.283C4.136 11.033 7.544 8.2 11.6 8.2h.514V4.483l5.431 5.431-5.43 5.431v-3.677l-.452-.055a9.59 9.59 0 0 0-1.175-.073Z"
                    />
                  </svg>
                  <span
                    class="text-xs"
                  >
                    Share
                  </span>
                </button>
                <div
                  class="absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden"
                />
              </div>
              <div />
            </div>
          </div>
          <div
            class="mx-4 md:mx-0"
          />
          <div
            class="mb-14 px-4 md:mb-20 md:px-0"
          />
        </div>
        <div
          class="w-full min-w-mrec space-y-14 px-4 md:space-y-0 md:p-0 lg:flex lg:w-3/10 lg:flex-col lg:space-y-10"
        >
          <div
            class="hidden empty:hidden lg:block"
          >
            <div
              class="sticky top-20"
            />
            <div
              class="hidden h-60 lg:block"
            />
            <div
              class="hidden h-60 lg:block"
            />
          </div>
          <div
            class="hidden grow lg:block"
          >
            <div
              class="sticky top-20"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
  <div
    class="sticky bottom-0 z-50 h-min"
    id="mostViewedNavBarDesktop"
  />
</div>
`;
