'use client';

import { useCallback, useContext, useEffect, useState } from 'react';

import { useAppSelector } from 'store/hooks';
import NavContext from 'themes/autumn/components/nav/NavContext';

import type { SpecificForcedStyle } from 'themes/autumn/components/nav/NavContext';

export function useSticky<T extends HTMLElement = HTMLDivElement>(): [
  (ref: T) => void,
  boolean,
] {
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const [stickyElement, setStickyElement] = useState<T>();
  const setStickyRef = useCallback(
    (ref: T) => {
      setStickyElement(ref);
    },
    [setStickyElement],
  );

  const [showSticky, setShowSticky] = useState(false);

  useEffect(() => {
    if (!stickyElement || !window.IntersectionObserver || hasTakeoverAd) {
      return;
    }

    // eslint-disable-next-line compat/compat
    const observer = new IntersectionObserver(
      ([e]) => {
        const isWindowTaller =
          window.innerHeight > stickyElement.getBoundingClientRect().top;
        setShowSticky(isWindowTaller && !e.isIntersecting);
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 0.00001,
      },
    );

    observer.observe(stickyElement);

    // eslint-disable-next-line consistent-return
    return () => {
      observer.disconnect();
    };
  }, [stickyElement, hasTakeoverAd]);

  return [setStickyRef, showSticky];
}

export function useForcedStyle(): SpecificForcedStyle | undefined {
  const { forcedStyle } = useContext(NavContext);
  if (typeof forcedStyle === 'object') {
    return forcedStyle;
  }
  if (forcedStyle) {
    return {
      mobile: forcedStyle,
      wide: forcedStyle,
    };
  }

  return undefined;
}
