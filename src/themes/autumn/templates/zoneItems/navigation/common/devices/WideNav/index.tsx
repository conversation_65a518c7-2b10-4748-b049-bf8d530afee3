'use client';

import clsx from 'clsx';
import { useContext, useEffect, useState } from 'react';
import TagManager from 'react-gtm-module';

import { useAppSelector } from 'store/hooks';
import { PageThemeVariant, RenderMode, ThemeVariant } from 'store/slices/conf';
import BillboardAd from 'themes/autumn/components/ads/BillboardAd';
import { LabelLogoType } from 'themes/autumn/components/generic/Logo/enums';
import NavContext from 'themes/autumn/components/nav/NavContext';
import {
  NavigationAdPosition,
  NavigationStyle,
} from 'themes/autumn/components/nav/NavContext/enums';
import { AbExperiment, useAbExperiment } from 'util/ab-tests';
import { DeviceType } from 'util/device';
import {
  useDeviceTypeFromWidth,
  useLazyLoadComponentState,
  useOnce,
  useTheme,
} from 'util/hooks';
import { isSponsoredPage } from 'util/page';
import { THEME } from 'util/theme';

import ShortcutsStrap from '../../elements/ShortcutsStrap';
import { useForcedStyle, useSticky } from '../../hooks';

import StaticWideHomepageNav from './StaticWideHomepageNav';
import StaticWideNav, { type StaticWideNavProps } from './StaticWideNav';
import StickyWideNav, { type StickyWideNavProps } from './StickyWideNav';

import type {
  HomepageWideNavFeatures,
  StaticWideNavFeatures,
  StickyWideNavFeatures,
} from 'types/Nav';
import type { NavigationZoneItem } from 'types/ZoneItems';

interface WideNavProps {
  homepageWideNavFeatures?: HomepageWideNavFeatures;
  navigationData: NavigationZoneItem['zoneItemData'];
  staticWideNavFeatures?: StaticWideNavFeatures;
  stickyWideNavFeatures?: StickyWideNavFeatures;
}

// In milliseconds
const AD_CONFIG = {
  AD_VIEW_TIME: 1500,
  ANIMATION_TIME: 500,
  TIMEOUT_IF_AD_DOESNOT_LOAD: 10000,
};

interface NavProps {
  staticNavProps?: StaticWideNavProps;
  stickyNavProps?: StickyWideNavProps;
}

function getNavProps(
  themeVariant: PageThemeVariant | ThemeVariant | undefined,
): NavProps {
  switch (themeVariant) {
    case PageThemeVariant.CLASSIFIEDS: {
      const commonProps = {
        breadcrumbEntryClassName: 'font-medium text-xs',
        labelLogoType: LabelLogoType.DEFAULT,
        showHomeInBreadcrumb: false,
      };
      return {
        staticNavProps: commonProps,
        stickyNavProps: commonProps,
      };
    }
    case PageThemeVariant.SPONSORED: {
      const commonProps = {
        labelLogoType: LabelLogoType.DEFAULT,
        showBreadcrumbs: false,
      };
      return {
        staticNavProps: {
          ...commonProps,
          containerClassName: 'border-b border-gray-300',
        },
        stickyNavProps: commonProps,
      };
    }
    case PageThemeVariant.SPORT: {
      const commonProps = {
        breadcrumbEntryClassName: 'font-medium text-xs',
        labelLogoType: LabelLogoType.DEFAULT,
        showHomeInBreadcrumb: false,
      };
      return {
        staticNavProps: commonProps,
        stickyNavProps: commonProps,
      };
    }
    case PageThemeVariant.EXPLORE_TRAVEL: {
      const commonProps = {
        labelLogoType: LabelLogoType.DEFAULT,
        showBreadcrumbs: false,
      };
      return {
        staticNavProps: {
          ...commonProps,
          containerClassName:
            THEME[ThemeVariant.EXPLORE]?.staticWideNav?.containerClassNames,
        },
        stickyNavProps: commonProps,
      };
    }
    case PageThemeVariant.HELP_CENTRE: {
      const commonProps = {
        labelLogoType: LabelLogoType.DEFAULT,
        showBreadcrumbs: false,
      };
      return {
        staticNavProps: commonProps,
        stickyNavProps: commonProps,
      };
    }
    default: {
      const commonProps = {
        labelLogoType: LabelLogoType.DEFAULT,
        showHomeInBreadcrumb: false,
      };
      return {
        staticNavProps: commonProps,
        stickyNavProps: commonProps,
      };
    }
  }
}

export default function WideNav({
  homepageWideNavFeatures,
  navigationData,
  staticWideNavFeatures,
  stickyWideNavFeatures,
}: WideNavProps): React.ReactElement {
  const [isAdStateActive, setAdStateActive] = useState(false);
  const [adState, setAdState] = useState({
    isAdRendered: false,
    isAnimate: false,
  });
  const [stickyRef, showSticky] = useSticky();

  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const weatherZoneEnabled = useAppSelector(
    (state) => state.features.weather.enabled,
  );
  const {
    forcedAdPosition,
    hideShortcuts,
    showNavigationAd,
    stickyWideContent,
  } = useContext(NavContext);

  const { showComponentPlaceholder } = useLazyLoadComponentState();

  const editmode =
    useAppSelector((state) => state.conf.mode) === RenderMode.EDIT;

  const hideBillboard =
    hasTakeoverAd ||
    !showComponentPlaceholder ||
    !showNavigationAd ||
    editmode;

  useEffect(() => {
    let adActiveTimer: number;
    if (!hideBillboard && !isAdStateActive) {
      adActiveTimer = window.setTimeout(() => {
        setAdStateActive(true);
      }, AD_CONFIG.TIMEOUT_IF_AD_DOESNOT_LOAD);
    }
    return () => {
      if (adActiveTimer) {
        clearTimeout(adActiveTimer);
      }
    };
  }, [hideBillboard, isAdStateActive]);

  useEffect(() => {
    let animateTimer: number;
    if (isAdStateActive && !adState.isAnimate && !adState.isAdRendered) {
      if (showSticky) {
        // Animate only if user has scrolled
        animateTimer = window.setTimeout(() => {
          setAdState({ isAdRendered: false, isAnimate: true });
        }, AD_CONFIG.AD_VIEW_TIME);
      } else {
        setAdState({ isAdRendered: true, isAnimate: false });
      }
    }
    return () => {
      if (animateTimer) {
        clearTimeout(animateTimer);
      }
    };
  }, [isAdStateActive, adState, showSticky]);

  useEffect(() => {
    let showAdtimer: number;
    if (isAdStateActive && adState.isAnimate && !adState.isAdRendered) {
      showAdtimer = window.setTimeout(() => {
        setAdState({ isAdRendered: true, isAnimate: false });
      }, AD_CONFIG.ANIMATION_TIME);
    }
    return () => {
      if (showAdtimer) {
        clearTimeout(showAdtimer);
      }
    };
  }, [isAdStateActive, adState]);

  const subscribeButtonOverride = useAppSelector(
    (state) => state.piano.subscribeButtonOverride,
  );
  const isDesktop = useDeviceTypeFromWidth() === DeviceType.DESKTOP;
  const abExperimentRunning = useAbExperiment(
    AbExperiment.NavSubscribeButton2,
  );
  useOnce(() => {
    if (abExperimentRunning && isDesktop && subscribeButtonOverride) {
      TagManager.dataLayer({
        dataLayer: {
          event: 'abtest_impression',
        },
      });
      return true;
    }
    return false;
  }, [abExperimentRunning, isDesktop, subscribeButtonOverride]);

  const templateName = useAppSelector((state) => state.layoutTemplate);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const forcedStyle = useForcedStyle();
  const themeVariant = useTheme();
  const isHomePage = forcedStyle?.wide
    ? forcedStyle.wide === NavigationStyle.HOMEPAGE
    : viewType === 'homepage';
  const useHomePage = isHomePage || templateName === 'autumn-header.html';
  const isAdAbove = forcedAdPosition
    ? forcedAdPosition === NavigationAdPosition.ABOVE
    : true;

  const stickyNav = (
    <StickyWideNav
      content={stickyWideContent}
      menuTree={navigationData.menuTree}
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...stickyWideNavFeatures}
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...getNavProps(themeVariant)?.stickyNavProps}
    />
  );

  const staticNav = useHomePage ? (
    <>
      <StaticWideHomepageNav
        menuTree={navigationData.menuTree}
        // eslint-disable-next-line react/jsx-props-no-spreading
        {...homepageWideNavFeatures}
        showWeather={weatherZoneEnabled}
      />
      {!hideShortcuts && (
        <ShortcutsStrap isForSiteNav navigationData={navigationData} />
      )}
    </>
  ) : (
    <StaticWideNav
      menuTree={navigationData.menuTree}
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...staticWideNavFeatures}
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...getNavProps(themeVariant)?.staticNavProps}
    />
  );

  const adBelow = (
    <>
      <div
        className={clsx({
          'fixed top-0 z-20 w-full': showSticky,
          hidden: hasTakeoverAd || !showSticky,
        })}
      >
        {stickyNav}
      </div>
      {hasTakeoverAd || !showSticky ? (
        staticNav
      ) : (
        <div className="invisible">{staticNav}</div>
      )}
      <div className="h-0 w-full" ref={stickyRef} />
      {!isSponsoredPage(viewType) &&
        showNavigationAd &&
        showComponentPlaceholder && (
          <div className="w-full">
            <BillboardAd />
          </div>
        )}
    </>
  );

  const adAbove = (
    <>
      {!isSponsoredPage(viewType) &&
        showNavigationAd &&
        showComponentPlaceholder && (
          <div className="w-full">
            <BillboardAd />
          </div>
        )}
      <div
        className={clsx('z-20 w-full', {
          'fixed top-0': showSticky,
          hidden: !showSticky || hasTakeoverAd,
        })}
      >
        {stickyNav}
      </div>
      {staticNav}
      {!hasTakeoverAd && <div className="h-0 w-full" ref={stickyRef} />}
    </>
  );

  return isAdAbove ? adAbove : adBelow;
}
