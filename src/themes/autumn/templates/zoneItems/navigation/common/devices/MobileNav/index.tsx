'use client';

import clsx from 'clsx';
import { useContext, useEffect, useRef, useState } from 'react';

import { useAppSelector } from 'store/hooks';
import { LabelLogoType } from 'themes/autumn/components/generic/Logo/enums';
import NavContext from 'themes/autumn/components/nav/NavContext';
import { useTheme } from 'util/hooks';
import useScrollDepthBreakpoint from 'util/scrollDepth';

import { useSticky } from '../../hooks';

import StaticMobileNav from './StaticMobileNav';
import StickyMobileNav from './StickyMobileNav';

import type { PageThemeVariant, ThemeVariant } from 'store/slices/conf';
import type {
  StaticMobileNavFeatures,
  StickyMobileNavFeatures,
} from 'types/Nav';

interface MobileNavProps {
  staticMobileNavFeatures?: StaticMobileNavFeatures;
  stickyMobileNavFeatures?: StickyMobileNavFeatures;
}

interface Props {
  labelLogoType: LabelLogoType;
  labelOfLogo?: string;
  logoTitleClassName?: string;
  urlOfLogo?: string;
}

function getMobileNavProps(
  templateName: string,
  themeVariant: PageThemeVariant | ThemeVariant,
): Props {
  switch (themeVariant) {
    default:
      return {
        labelLogoType: LabelLogoType.DEFAULT,
      };
  }
}

export default function MobileNav({
  staticMobileNavFeatures,
  stickyMobileNavFeatures,
}: MobileNavProps): React.ReactElement {
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const templateName = useAppSelector((state) => state.layoutTemplate);
  const weatherZoneEnabled = useAppSelector(
    (state) => state.features.weather.enabled,
  );

  const themeVariant = useTheme();
  const [stickyRef, showSticky] = useSticky();
  const { stickyMobileContent } = useContext(NavContext);

  const mostViewedRef = useRef<HTMLDivElement>(null);
  const hasReachedScrollBreakpoint = useScrollDepthBreakpoint(30);
  const [hasMostViewedModule, setHasMostViewedModule] = useState(false);

  useEffect(() => {
    // Check if MostViewedNavBar has been updated with the piano module
    // then use the StaticMobileNav with the brand logo instead
    if (!mostViewedRef?.current || hasMostViewedModule) {
      return undefined;
    }

    const observer = new MutationObserver(() => {
      const firstChild = mostViewedRef.current?.firstChild;
      if (firstChild && firstChild.firstChild) {
        setHasMostViewedModule(true);
        observer.disconnect();
      }
    });

    observer.observe(mostViewedRef.current, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [hasMostViewedModule]);

  return (
    <>
      <div
        className={clsx(
          'group fixed top-0 z-40 w-full transition-all duration-150 ease-in print:invisible',
          showSticky ? 'visible opacity-100' : 'invisible opacity-0',
          {
            hidden: hasTakeoverAd,
          },
        )}
      >
        {hasMostViewedModule ? (
          <StaticMobileNav
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...staticMobileNavFeatures}
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...getMobileNavProps(templateName, themeVariant)}
            showSubscribeLoginButtons={false}
            showWeather={weatherZoneEnabled}
          />
        ) : (
          <StickyMobileNav
            content={stickyMobileContent}
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...stickyMobileNavFeatures}
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...getMobileNavProps(templateName, themeVariant)}
          />
        )}
        <div
          className={clsx(
            'h-min transition-all duration-300 ease-in',
            showSticky && hasReachedScrollBreakpoint
              ? 'translate-y-0 opacity-100'
              : '-translate-y-full opacity-0',
          )}
          id="mostViewedNavBarMobile"
          ref={mostViewedRef}
        />
      </div>
      <div className="z-50">
        <StaticMobileNav
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...staticMobileNavFeatures}
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...getMobileNavProps(templateName, themeVariant)}
          showWeather={weatherZoneEnabled}
        />
        <div className="h-0 w-full" ref={stickyRef} />
      </div>
    </>
  );
}
