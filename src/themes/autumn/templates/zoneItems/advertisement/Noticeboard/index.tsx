import clsx from 'clsx';
import { useState } from 'react';

import Ad from 'themes/autumn/components/ads/Ad';
import { AdSize } from 'util/ads';
import { useLazyLoadComponentState } from 'util/hooks';

import type { AdvertisementZoneItem } from 'types/ZoneItems';

export default function Noticeboard({
  zoneItemData: { position },
  zoneItemId,
}: AdvertisementZoneItem): React.ReactElement | null {
  const { showComponentPlaceholder } = useLazyLoadComponentState();
  const [adLoaded, setAdLoaded] = useState(false);

  if (!showComponentPlaceholder) {
    return null;
  }

  return (
    <div className={clsx('my-6', { hidden: !adLoaded })}>
      <Ad
        autoRefresh={false}
        mdSizes={AdSize.noticeboardDesktop}
        onRender={(e) => {
          if (!e.event.isEmpty) {
            setAdLoaded(true);
          }
        }}
        position={position}
        publiftName={`incontent-hrec-lg-${Math.min(4, position)}`}
        sizes={AdSize.noticeboardMobile}
        slotId={`noticeboard-${zoneItemId}`}
      />
    </div>
  );
}
