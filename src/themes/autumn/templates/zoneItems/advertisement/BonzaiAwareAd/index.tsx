'use client';

import { useEffect } from 'react';

import { useAppSelector } from 'store/hooks';
import Ad from 'themes/autumn/components/ads/Ad';
import AdProvider from 'themes/autumn/components/ads/AdProvider';

import type { Props as AdProps } from 'themes/autumn/components/ads/Ad';

interface Props extends AdProps {
  mdSizesWithBonzai: AdProps['mdSizes'];
}

export default function BonzaiAwareAd({
  mdSizes,
  mdSizesWithBonzai,
  slotId,
  ...props
}: Props) {
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);

  // Refresh the ad slot when a Bonzai ad appears, because the desired ad size
  // changes. Checking for `hasTakeoverAd` is not enough, as it may have not
  // loaded yet, so poll for `body.has-bonzai`.
  useEffect(() => {
    let tries = 0;
    const id = setInterval(() => {
      if (hasTakeoverAd && document.body.classList.contains('has-bonzai')) {
        AdProvider.refresh(slotId);
        clearInterval(id);
      } else {
        tries += 1;
      }

      // Sometimes, Ad Ops forgets to include the code for setting the body
      // class, so give up after some tries
      if (tries > 10) {
        clearInterval(id);
      }
    }, 500);

    return () => {
      clearInterval(id);
    };
  }, [hasTakeoverAd, slotId]);

  return (
    <Ad
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
      mdSizes={hasTakeoverAd ? mdSizesWithBonzai : mdSizes}
      slotId={slotId}
    />
  );
}
