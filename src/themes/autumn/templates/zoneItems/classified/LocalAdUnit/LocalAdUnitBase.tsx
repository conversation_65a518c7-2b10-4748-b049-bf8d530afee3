'use client';

import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import React, { useEffect, useRef, useState } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { useAppSelector } from 'store/hooks';
import Container from 'themes/autumn/components/generic/Container';
import Link from 'themes/autumn/components/generic/Link';
import Heading from 'themes/autumn/components/page/StrapHeading';
import CarouselAds from 'themes/autumn/templates/zoneItems/classified/LocalAdUnit/CarouselAds';
import Classifieds from 'themes/autumn/templates/zoneItems/classified/LocalAdUnit/Classifieds';
import { usePremiumSubscription } from 'util/hooks';
import { LOGO_ICONS } from 'util/icons';
import { fetchPromotions } from 'util/organization/suzuka';

import { useOnScreen } from '../hooks';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import styles from './classifieds.module.css';

import type { PromotionResponse } from 'util/organization/suzuka';

interface LocalAdUnitBaseProps {
  carouselBreakpoint?: string;
  isMopStory?: boolean;
  showHeading?: boolean;
  verticalStackBreakpoint?: string;
}

interface ViewInsuranceBannerProps {
  asPlaceholder: boolean;
  hasTakeoverAd: boolean;
  isNewStyle?: boolean;
}

const VIEW_INSURANCE_URL = 'https://viewinsurance.com.au/business-insurance/';

function ViewInsuranceBanner({
  asPlaceholder,
  hasTakeoverAd = false,
  isNewStyle = false,
}: ViewInsuranceBannerProps): React.ReactElement {
  return (
    <Link
      className={clsx('not-prose -mx-4 flex font-inter md:mx-0', {
        'pointer-events-none': asPlaceholder,
      })}
      href={VIEW_INSURANCE_URL}
      noStyle
      target="_blank"
    >
      <div
        className={clsx('flex w-full items-center justify-center md:mt-6', {
          'border-b-1 bg-gray-100 pb-5 sm:bg-transparent md:bg-white':
            !isNewStyle,
        })}
      >
        <div
          className={clsx('shrink-0', {
            'opacity-0': asPlaceholder,
          })}
        >
          {LOGO_ICONS.VIEW_INSURANCE}
        </div>
        <div
          className={clsx(
            'ml-3 text-xs md:text-sm lg:text-xs',
            hasTakeoverAd ? 'xl:text-xs' : 'xl:text-sm',
            { 'opacity-0': asPlaceholder },
          )}
        >
          <div>Need Insurance?</div>
          <div
            className={clsx(
              'font-semibold hover:opacity-80',
              styles.viewInsurance,
            )}
          >
            Contact View Insurance today
            <FontAwesomeIcon
              className="ml-2"
              color={styles.viewInsurance}
              icon={faArrowRight}
            />
          </div>
        </div>
      </div>
    </Link>
  );
}

function LocalAdUnitBase({
  carouselBreakpoint = 'lg:hidden',
  isMopStory = false,
  showHeading = false,
  verticalStackBreakpoint = 'hidden lg:block',
}: LocalAdUnitBaseProps): React.ReactElement | null {
  const [promotions, setPromotions] = useState<PromotionResponse[]>([]);
  const [isCarousel, setIsCarousel] = useState<boolean | undefined>(undefined);
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);

  const refCarousel = useRef<HTMLDivElement>(null);
  const refVertical = useRef<HTMLDivElement>(null);

  const isCarouselVisible = useOnScreen(refCarousel);
  const isVerticalVisible = useOnScreen(refVertical);

  const {
    hasPremiumExtended,
    hasValidatedPremium,
    isPremiumRequest,
    supportPremiumExtended,
  } = usePremiumSubscription();

  const updatePromotion = (index: number, showNum: boolean): void => {
    const updatedPromotions = promotions.map((item, key) => {
      if (key === index) {
        return {
          ...item,
          showNum,
        };
      }
      return {
        ...item,
      };
    });
    setPromotions(updatedPromotions);
  };

  useEffect(() => {
    if (
      (!isCarouselVisible || isCarousel === true) &&
      (!isVerticalVisible || isCarousel === false)
    ) {
      return;
    }

    const limit = isCarouselVisible ? 6 : 3;

    setIsCarousel(isCarouselVisible);

    fetchPromotions({ limit })
      .then((res) => {
        const response = res.map((item) => ({
          ...item,
          showNum: false,
        }));
        setPromotions(response);
      })
      .catch(() => {
        setPromotions([]);
      });
  }, [isCarousel, isCarouselVisible, isVerticalVisible, setPromotions]);

  if (!isClientSide) {
    return null;
  }

  const hideForPremium = supportPremiumExtended && hasPremiumExtended;
  const viewInsuranceComponent = !isPremiumRequest &&
    (!hasValidatedPremium || !hideForPremium) && (
      <ViewInsuranceBanner
        asPlaceholder={!hasValidatedPremium}
        hasTakeoverAd={hasTakeoverAd}
        isNewStyle={showHeading}
      />
    );

  return (
    <>
      <div
        className={carouselBreakpoint}
        data-testid="classifieds-carousel-wrap"
        ref={refCarousel}
      >
        {isCarousel !== undefined && showHeading && !!promotions.length && (
          <Heading
            className={clsx('mb-3 mt-8', {
              'border-b border-solid border-gray-900': isMopStory,
            })}
            hideLine={isMopStory}
          >
            <div
              className={clsx({
                'mb-5 text-heading4 font-semibold text-gray-900': isMopStory,
              })}
            >
              Local Business Listings
            </div>
          </Heading>
        )}
        {isCarousel === true && !!promotions.length && (
          <div
            className={clsx({
              'mt-8': !showHeading,
            })}
          >
            <CarouselAds
              ads={promotions}
              isNewStyle={showHeading}
              updateAds={updatePromotion}
            />
            {viewInsuranceComponent}
          </div>
        )}
      </div>
      <div
        className={verticalStackBreakpoint}
        data-testid="classifieds-vertical-wrap"
        ref={refVertical}
      >
        {isCarousel === false && !!promotions.length && (
          <>
            <Container className="flex flex-col gap-2" noGutter>
              {promotions.map((item, key) => (
                <Classifieds
                  index={key}
                  isNewStyle={showHeading}
                  isVertical
                  item={item}
                  key={item.id}
                  setPromotions={updatePromotion}
                />
              ))}
            </Container>
            {viewInsuranceComponent}
          </>
        )}
      </div>
    </>
  );
}

export default LocalAdUnitBase;
